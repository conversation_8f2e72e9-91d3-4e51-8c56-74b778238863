import React, { useState, useCallback, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { CheckCircle, Circle, Info, AlertTriangle } from 'lucide-react';
import { ANXIETY_DISORDERS } from '../data/anxiety-disorders';
import { validateDisorderCriteria } from '../utils/diagnosticValidation';
import type { DisorderAssessment, CriterionTemplate } from '../types';

interface AnxietyDisordersAssessmentProps {
  patientId: string;
  onAssessmentChange: (assessment: Partial<DisorderAssessment>) => void;
  initialData?: Partial<DisorderAssessment>;
}

const AnxietyDisordersAssessment: React.FC<AnxietyDisordersAssessmentProps> = ({
  patientId,
  onAssessmentChange,
  initialData
}) => {
  const [selectedDisorder, setSelectedDisorder] = useState<string>(
    initialData?.disorderId || ANXIETY_DISORDERS[0].id
  );
  const [criteriaResponses, setCriteriaResponses] = useState<Record<string, boolean>>(
    initialData?.criteria?.reduce((acc, c) => ({ ...acc, [c.id]: c.present }), {}) || {}
  );
  const [severityRatings, setSeverityRatings] = useState<Record<string, number>>(
    initialData?.criteria?.reduce((acc, c) => ({ ...acc, [c.id]: c.severity || 1 }), {}) || {}
  );
  const [notes, setNotes] = useState<Record<string, string>>(
    initialData?.criteria?.reduce((acc, c) => ({ ...acc, [c.id]: c.comments || '' }), {}) || {}
  );

  const selectedDisorderData = useMemo(() => 
    ANXIETY_DISORDERS.find(d => d.id === selectedDisorder)!,
    [selectedDisorder]
  );

  const assessmentProgress = useMemo(() => {
    if (!selectedDisorderData) return { totalCriteria: 0, completedCriteria: 0, percentComplete: 0 };

    const validation = validateDisorderCriteria(selectedDisorder, criteriaResponses);
    const totalCriteria = selectedDisorderData.criteria.length;
    const completedCriteria = Object.keys(criteriaResponses).length;
    const requiredCriteria = selectedDisorderData.criteria.filter(c => c.required).length;
    const metCriteria = validation.metCriteria.length;

    return {
      totalCriteria,
      completedCriteria,
      requiredCriteria,
      metCriteria,
      percentComplete: totalCriteria > 0 ? (completedCriteria / totalCriteria) * 100 : 0,
      isValid: validation.isValid,
      canDiagnose: validation.canDiagnose,
      missingRequired: validation.missingRequired
    };
  }, [selectedDisorderData, selectedDisorder, criteriaResponses]);

  const handleCriterionChange = useCallback((criterionId: string, present: boolean) => {
    const newResponses = { ...criteriaResponses, [criterionId]: present };
    setCriteriaResponses(newResponses);
    updateAssessment(newResponses);
  }, [criteriaResponses]);

  const handleSeverityChange = useCallback((criterionId: string, severity: number) => {
    setSeverityRatings(prev => ({ ...prev, [criterionId]: severity }));
    updateAssessment();
  }, []);

  const handleNotesChange = useCallback((criterionId: string, note: string) => {
    setNotes(prev => ({ ...prev, [criterionId]: note }));
    updateAssessment();
  }, []);

  const updateAssessment = (newResponses = criteriaResponses) => {
    if (!selectedDisorderData) return;

    const criteria = selectedDisorderData.criteria.map(criterion => ({
      id: criterion.id,
      code: criterion.code,
      description: criterion.description,
      present: newResponses[criterion.id] || false,
      severity: severityRatings[criterion.id] || 1,
      comments: notes[criterion.id] || ''
    }));

    const assessment: Partial<DisorderAssessment> = {
      disorderId: selectedDisorder,
      patientId,
      criteria,
      assessmentDate: new Date().toISOString(),
      status: 'draft'
    };

    onAssessmentChange(assessment);
  };

  const renderCriterion = (criterion: CriterionTemplate) => {
    const isPresent = criteriaResponses[criterion.id];
    const severity = severityRatings[criterion.id] || 1;
    const note = notes[criterion.id] || '';

    return (
      <Card key={criterion.id} className={`mb-4 ${isPresent ? 'ring-2 ring-blue-200' : ''}`}>
        <CardContent className="pt-4">
          <div className="flex items-start space-x-3">
            <button
              onClick={() => handleCriterionChange(criterion.id, !isPresent)}
              className="mt-1 flex-shrink-0"
            >
              {isPresent ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <Circle className="h-5 w-5 text-gray-400" />
              )}
            </button>
            
            <div className="flex-1 space-y-3">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Badge variant="outline">{criterion.code}</Badge>
                  {criterion.required && (
                    <Badge variant="destructive" className="text-xs">Required</Badge>
                  )}
                </div>
                <p className="text-sm text-gray-700">{criterion.description}</p>
              </div>

              {criterion.subCriteria && criterion.subCriteria.length > 0 && (
                <div className="ml-4 space-y-2">
                  <h5 className="text-sm font-medium text-gray-600">Sub-criteria:</h5>
                  {criterion.subCriteria.map(subCriterion => (
                    <div key={subCriterion.id} className="flex items-start space-x-2">
                      <button
                        onClick={() => handleCriterionChange(subCriterion.id, !criteriaResponses[subCriterion.id])}
                        className="mt-0.5 flex-shrink-0"
                      >
                        {criteriaResponses[subCriterion.id] ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <Circle className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                      <div className="flex-1">
                        <Badge variant="outline" className="text-xs mb-1">{subCriterion.code}</Badge>
                        <p className="text-xs text-gray-600">{subCriterion.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {isPresent && (
                <div className="space-y-3 pt-2 border-t border-gray-100">
                  <div>
                    <label className="text-xs font-medium text-gray-600 mb-2 block">
                      Severity (1-5): {severity}
                    </label>
                    <Slider
                      value={[severity]}
                      onValueChange={(value) => handleSeverityChange(criterion.id, value[0])}
                      max={5}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                  </div>
                  
                  <div>
                    <label className="text-xs font-medium text-gray-600 mb-1 block">
                      Clinical Notes
                    </label>
                    <Textarea
                      value={note}
                      onChange={(e) => handleNotesChange(criterion.id, e.target.value)}
                      placeholder="Additional clinical observations..."
                      className="text-sm"
                      rows={2}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Disorder Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>Anxiety Disorders Assessment</span>
            <Badge variant="secondary">{ANXIETY_DISORDERS.length} disorders</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {ANXIETY_DISORDERS.map(disorder => (
              <button
                key={disorder.id}
                onClick={() => setSelectedDisorder(disorder.id)}
                className={`p-3 text-left border rounded-lg transition-colors ${
                  selectedDisorder === disorder.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="font-medium text-sm">{disorder.name}</div>
                <div className="text-xs text-gray-500 mt-1">{disorder.code}</div>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Assessment Progress */}
      <Card>
        <CardContent className="pt-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Assessment Progress</span>
            <span className="text-sm text-gray-600">
              {assessmentProgress.completedCriteria}/{assessmentProgress.totalCriteria} criteria
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${assessmentProgress.percentComplete}%` }}
            />
          </div>
          
          {assessmentProgress.canDiagnose && (
            <div className="flex items-center space-x-2 text-green-700 bg-green-50 p-2 rounded">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm">Sufficient criteria met for diagnosis</span>
            </div>
          )}
          
          {assessmentProgress.missingRequired.length > 0 && (
            <div className="flex items-center space-x-2 text-amber-700 bg-amber-50 p-2 rounded">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm">Missing required criteria: {assessmentProgress.missingRequired.join(', ')}</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Disorder Information */}
      {selectedDisorderData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>{selectedDisorderData.name}</span>
              <Badge variant="outline">{selectedDisorderData.code}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                <p className="text-sm text-gray-600">{selectedDisorderData.description}</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Key Diagnostic Features</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {selectedDisorderData.diagnosticFeatures.map((feature, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <Info className="h-3 w-3 mt-1 text-blue-500 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Criteria Assessment */}
      {selectedDisorderData && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Diagnostic Criteria</h3>
          {selectedDisorderData.criteria.map(criterion => 
            renderCriterion(criterion)
          )}
        </div>
      )}
    </div>
  );
};

export default AnxietyDisordersAssessment;
