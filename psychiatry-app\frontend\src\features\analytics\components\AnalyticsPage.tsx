import React, { useState } from 'react';
import {
  Users,
  Calendar,
  FileText,
  TrendingUp,
  Activity,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { useDashboardAnalytics, usePatientAnalytics, useAppointmentAnalytics, useLabResultAnalytics } from '../hooks/useAnalytics';
import LoadingSpinner from '../../../components/ui/LoadingSpinner';
import { Card, CardHeader, CardTitle, CardContent } from '../../../components/ui';
import type { DateRange } from '../types';

const AnalyticsPage: React.FC = () => {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    to: new Date().toISOString().split('T')[0], // today
  });

  const { analytics: dashboardData, loading: dashboardLoading, error: dashboardError } = useDashboardAnalytics();
  const { analytics: patientData, loading: patientLoading } = usePatientAnalytics(dateRange);
  const { analytics: appointmentData, loading: appointmentLoading } = useAppointmentAnalytics(dateRange);
  const { analytics: labData, loading: labLoading } = useLabResultAnalytics(dateRange);

  const handleDateRangeChange = (field: 'from' | 'to', value: string) => {
    setDateRange(prev => ({ ...prev, [field]: value }));
  };

  if (dashboardLoading) {
    return <LoadingSpinner />;
  }

  if (dashboardError) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">{dashboardError}</p>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center mx-auto"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">From:</label>
            <input
              type="date"
              value={dateRange.from}
              onChange={(e) => handleDateRangeChange('from', e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            />
          </div>
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">To:</label>
            <input
              type="date"
              value={dateRange.to}
              onChange={(e) => handleDateRangeChange('to', e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            />
          </div>
        </div>
      </div>

      {/* Dashboard Overview Cards */}
      {dashboardData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Patients Card */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Patients</p>
                  <p className="text-2xl font-bold text-gray-900">{dashboardData?.patients?.total || 0}</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-4 w-4 text-blue-500 mr-1" />
                    <span className="text-sm text-blue-600">
                      {dashboardData?.patients?.recent || 0} recent
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Appointments Card */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Appointments</p>
                  <p className="text-2xl font-bold text-gray-900">{dashboardData?.appointments?.total || 0}</p>
                  <p className="text-sm text-gray-500">
                    {dashboardData?.appointments?.upcoming || 0} upcoming
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Lab Results Card */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <FileText className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Lab Results</p>
                  <p className="text-2xl font-bold text-gray-900">{dashboardData?.labResults?.total || 0}</p>
                  <div className="flex items-center mt-1">
                    <AlertTriangle className="h-4 w-4 text-yellow-500 mr-1" />
                    <span className="text-sm text-yellow-600">
                      {dashboardData?.labResults?.recent || 0} recent
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* System Card (Admin only) */}
          {dashboardData?.system && (
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <Activity className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">System Users</p>
                    <p className="text-2xl font-bold text-gray-900">{dashboardData?.system?.totalUsers || 0}</p>
                    <p className="text-sm text-gray-500">
                      {dashboardData?.system?.totalUsers || 0} total users
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Detailed Analytics Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Patient Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Patient Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            {patientLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : patientData ? (
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Demographics</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Gender Distribution</p>
                      {patientData.demographics?.gender ?
                        Object.entries(patientData.demographics.gender).map(([gender, count]) => (
                          <div key={gender} className="flex justify-between">
                            <span className="text-sm">{gender}</span>
                            <span className="text-sm font-medium">{String(count)}</span>
                          </div>
                        )) : (
                          <div className="text-sm text-gray-500">No gender data available</div>
                        )
                      }
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Engagement</p>
                      <div className="flex justify-between">
                        <span className="text-sm">With Appointments</span>
                        <span className="text-sm font-medium">
                          {patientData.engagement?.appointmentRate?.toFixed(1) ?? '0.0'}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">With Lab Results</span>
                        <span className="text-sm font-medium">
                          {patientData.engagement?.labResultRate?.toFixed(1) ?? '0.0'}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No patient data available</p>
            )}
          </CardContent>
        </Card>

        {/* Appointment Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Appointment Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            {appointmentLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : appointmentData ? (
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Status Distribution</h4>
                  {appointmentData.overview?.statusDistribution ?
                    Object.entries(appointmentData.overview.statusDistribution).map(([status, count]) => (
                      <div key={status} className="flex justify-between">
                        <span className="text-sm">{status}</span>
                        <span className="text-sm font-medium">{String(count)}</span>
                      </div>
                    )) : (
                      <div className="text-sm text-gray-500">No status data available</div>
                    )
                  }
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Performance</h4>
                  <div className="flex justify-between">
                    <span className="text-sm">Avg Duration</span>
                    <span className="text-sm font-medium">
                      {appointmentData.performance?.duration?.avg_minutes || 'N/A'} min
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No appointment data available</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Lab Results Analytics */}
      {labData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Lab Results Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            {labLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : labData ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Test Type Distribution</h4>
                  {labData.overview?.testTypeDistribution ?
                    Object.entries(labData.overview.testTypeDistribution).slice(0, 5).map(([type, count]) => (
                      <div key={type} className="flex justify-between">
                        <span className="text-sm">{type}</span>
                        <span className="text-sm font-medium">{String(count)}</span>
                      </div>
                    )) : (
                      <div className="text-sm text-gray-500">No test type data available</div>
                    )
                  }
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Quality Metrics</h4>
                  <div className="flex justify-between">
                    <span className="text-sm">Avg Turnaround</span>
                    <span className="text-sm font-medium">
                      {labData.quality?.turnaroundTime?.avg_hours?.toFixed(1) ?? '0.0'}h
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Flagged Results</span>
                    <span className="text-sm font-medium">
                      {labData.quality?.flaggedByTestType?.length ?? 0}
                    </span>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Status Distribution</h4>
                  {labData.overview?.statusDistribution ?
                    Object.entries(labData.overview.statusDistribution).map(([status, count]) => (
                      <div key={status} className="flex justify-between">
                        <span className="text-sm">{status}</span>
                        <span className="text-sm font-medium">{String(count)}</span>
                      </div>
                    )) : (
                      <div className="text-sm text-gray-500">No status data available</div>
                    )
                  }
                </div>
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No lab data available</p>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AnalyticsPage;
