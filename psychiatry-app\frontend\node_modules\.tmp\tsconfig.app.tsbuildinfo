{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/apierrorboundary.tsx", "../../src/components/errorboundary.tsx", "../../src/components/labdatamanager.tsx", "../../src/components/labmanagementpage.tsx", "../../src/components/patientselector.tsx", "../../src/components/psychtestingpage.tsx", "../../src/components/medicationhistory/medicationtimeline.tsx", "../../src/components/medicationhistory/medicationtimelineenhanced.tsx", "../../src/components/mentalstatusexam/mentalstatusexamform.tsx", "../../src/components/psychtesting/gad7.tsx", "../../src/components/psychtesting/phq9.tsx", "../../src/components/psychtesting/psychtestingdashboard.tsx", "../../src/components/psychtesting/testhistory.tsx", "../../src/components/layout/layout.tsx", "../../src/components/ui/loadingspinner.tsx", "../../src/components/ui/modal.tsx", "../../src/components/ui/table.tsx", "../../src/components/ui/toast.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/index.ts", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/textarea.tsx", "../../src/data/labtemplates.ts", "../../src/features/analytics/index.ts", "../../src/features/analytics/types.ts", "../../src/features/analytics/components/analyticspage.tsx", "../../src/features/analytics/hooks/useanalytics.ts", "../../src/features/analytics/services/analyticsapi.ts", "../../src/features/appointments/index.ts", "../../src/features/appointments/types.ts", "../../src/features/appointments/components/addappointmentpage.tsx", "../../src/features/appointments/components/appointmentspage.tsx", "../../src/features/appointments/hooks/useappointments.ts", "../../src/features/appointments/services/appointmentsapi.ts", "../../src/features/auth/index.ts", "../../src/features/auth/types.ts", "../../src/features/auth/components/authprovider.tsx", "../../src/features/auth/components/loginpage.tsx", "../../src/features/auth/components/protectedroute.tsx", "../../src/features/auth/hooks/useauth.ts", "../../src/features/auth/services/authservice.ts", "../../src/features/dashboard/index.ts", "../../src/features/dashboard/types.ts", "../../src/features/dashboard/components/dashboardpage.tsx", "../../src/features/history-taking/index.ts", "../../src/features/history-taking/types.ts", "../../src/features/history-taking/components/anxietydisordersassessment.tsx", "../../src/features/history-taking/components/historytakingpage.tsx", "../../src/features/history-taking/components/mooddisordersassessment.tsx", "../../src/features/history-taking/components/patientselectionpage.tsx", "../../src/features/history-taking/components/psychoticdisordersassessment.tsx", "../../src/features/history-taking/data/anxiety-disorders.ts", "../../src/features/history-taking/data/disorders-registry.ts", "../../src/features/history-taking/data/mood-disorders.ts", "../../src/features/history-taking/data/obsessive-compulsive-disorders.ts", "../../src/features/history-taking/data/psychotic-disorders.ts", "../../src/features/history-taking/data/trauma-stressor-disorders.ts", "../../src/features/history-taking/services/historytakingservice.ts", "../../src/features/lab-results/index.ts", "../../src/features/lab-results/types.ts", "../../src/features/lab-results/components/addlabresultpage.tsx", "../../src/features/lab-results/components/labresultspage.tsx", "../../src/features/lab-results/hooks/uselabresults.ts", "../../src/features/lab-results/services/labresultsapi.ts", "../../src/features/patients/index.ts", "../../src/features/patients/types.ts", "../../src/features/patients/components/addpatientpage.tsx", "../../src/features/patients/components/editpatientpage.tsx", "../../src/features/patients/components/patientcard.tsx", "../../src/features/patients/components/patientdetailspage.tsx", "../../src/features/patients/components/patientform.tsx", "../../src/features/patients/components/patientspage.tsx", "../../src/features/patients/hooks/usepatient.ts", "../../src/features/patients/hooks/usepatients.ts", "../../src/features/patients/services/patientsapi.ts", "../../src/features/psychological-testing/types.ts", "../../src/features/psychological-testing/components/psychtestingdashboard.tsx", "../../src/features/psychological-testing/tests/gad7.ts", "../../src/features/psychological-testing/tests/phq9.ts", "../../src/features/users/index.ts", "../../src/features/users/types.ts", "../../src/features/users/components/adduserpage.tsx", "../../src/features/users/components/userform.tsx", "../../src/features/users/components/userspage.tsx", "../../src/features/users/hooks/useusers.ts", "../../src/features/users/services/usersapi.ts", "../../src/hooks/use-toast.tsx", "../../src/hooks/useapierror.ts", "../../src/lib/api.ts", "../../src/lib/performance.tsx", "../../src/lib/querykeys.ts", "../../src/lib/serviceworker.ts", "../../src/lib/utils.ts", "../../src/providers/queryprovider.tsx", "../../src/store/formstore.ts", "../../src/store/patientstore.ts", "../../src/store/uistore.ts", "../../src/test/app.test.tsx", "../../src/test/setup.ts", "../../src/utils/psychtestscoring.ts"], "errors": true, "version": "5.8.3"}