import type { CriterionTemplate, SpecifierTemplate, DisorderAssessment } from '../types';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
  completeness?: number; // 0-1 scale
}

export interface CriteriaValidationResult {
  criterionId: string;
  isValid: boolean;
  severity?: number;
  notes?: string;
  errors: string[];
}

/**
 * Validates disorder criteria based on DSM-5 requirements
 */
export function validateDisorderCriteria(
  criteria: CriterionTemplate[],
  responses: Record<string, any>,
  specifiers?: SpecifierTemplate[]
): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let validCriteria = 0;
  let totalRequired = 0;

  // Validate each criterion
  criteria.forEach(criterion => {
    if (criterion.required) {
      totalRequired++;
    }

    const response = responses[criterion.id];
    const validation = validateSingleCriterion(criterion, response);
    
    if (criterion.required && !validation.isValid) {
      errors.push(`Required criterion "${criterion.text}" is not met`);
    } else if (validation.isValid) {
      validCriteria++;
    }

    // Add any specific errors from the criterion validation
    errors.push(...validation.errors);
  });

  // Validate specifiers if provided
  if (specifiers) {
    specifiers.forEach(specifier => {
      const response = responses[`specifier_${specifier.id}`];
      if (response && !specifier.options.includes(response)) {
        errors.push(`Invalid specifier value for "${specifier.name}"`);
      }
    });
  }

  // Calculate completeness
  const completeness = totalRequired > 0 ? validCriteria / totalRequired : 0;

  // Add warnings for low completeness
  if (completeness < 0.5) {
    warnings.push('Assessment appears incomplete. Consider reviewing all criteria.');
  }

  return {
    isValid: errors.length === 0 && completeness >= 0.8,
    errors,
    warnings,
    completeness
  };
}

/**
 * Validates a single criterion
 */
export function validateSingleCriterion(
  criterion: CriterionTemplate,
  response: any
): CriteriaValidationResult {
  const errors: string[] = [];
  let isValid = false;

  if (!response) {
    if (criterion.required) {
      errors.push('Response is required for this criterion');
    }
    return { criterionId: criterion.id, isValid: false, errors };
  }

  switch (criterion.type) {
    case 'symptom':
      isValid = validateSymptomCriterion(response);
      break;
    case 'duration':
      isValid = validateDurationCriterion(response);
      break;
    case 'impairment':
      isValid = validateImpairmentCriterion(response);
      break;
    case 'exclusion':
      isValid = validateExclusionCriterion(response);
      break;
    case 'functional':
      isValid = validateFunctionalCriterion(response);
      break;
    default:
      errors.push(`Unknown criterion type: ${criterion.type}`);
  }

  return {
    criterionId: criterion.id,
    isValid,
    severity: response.severity,
    notes: response.notes,
    errors
  };
}

/**
 * Validates symptom presence and severity
 */
function validateSymptomCriterion(response: any): boolean {
  return response.present === true && (response.severity || 0) >= 1;
}

/**
 * Validates duration requirements
 */
function validateDurationCriterion(response: any): boolean {
  return response.duration && response.duration > 0;
}

/**
 * Validates functional impairment
 */
function validateImpairmentCriterion(response: any): boolean {
  return response.impairment === true || (response.severity || 0) >= 2;
}

/**
 * Validates exclusion criteria (should be false/absent)
 */
function validateExclusionCriterion(response: any): boolean {
  return response.present === false || response.excluded === true;
}

/**
 * Validates functional criteria
 */
function validateFunctionalCriterion(response: any): boolean {
  return response.functional === true || (response.severity || 0) >= 1;
}

/**
 * Generates a diagnostic summary based on validation results
 */
export function generateDiagnosticSummary(
  assessment: DisorderAssessment,
  validationResult: ValidationResult
): string {
  const { isValid, completeness, errors, warnings } = validationResult;
  
  let summary = `Assessment for ${assessment.disorderName}:\n`;
  summary += `Completeness: ${Math.round((completeness || 0) * 100)}%\n`;
  
  if (isValid) {
    summary += 'Status: Criteria appear to be met\n';
  } else {
    summary += 'Status: Criteria not fully met\n';
  }
  
  if (errors.length > 0) {
    summary += `\nIssues identified:\n${errors.map(e => `- ${e}`).join('\n')}`;
  }
  
  if (warnings && warnings.length > 0) {
    summary += `\nWarnings:\n${warnings.map(w => `- ${w}`).join('\n')}`;
  }
  
  return summary;
}

/**
 * Validates assessment completeness
 */
export function validateAssessmentCompleteness(assessment: DisorderAssessment): ValidationResult {
  const errors: string[] = [];
  
  if (!assessment.responses || Object.keys(assessment.responses).length === 0) {
    errors.push('No responses provided');
  }
  
  if (!assessment.disorderName) {
    errors.push('Disorder name is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
