import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Brain, 
  ClipboardList, 
  TrendingUp, 
  AlertTriangle, 
  Clock, 
  User,
  FileText,
  BarChart3
} from 'lucide-react';
import { PHQ9_TEST } from '../tests/phq9';
import { GAD7_TEST } from '../tests/gad7';
import type { TestDefinition, PatientTestHistory } from '../types';

interface PsychTestingDashboardProps {
  patientId?: string;
  onTestSelect: (testId: string, patientId: string) => void;
  onViewHistory: (patientId: string) => void;
  onViewReports: (patientId: string) => void;
}

const AVAILABLE_TESTS: TestDefinition[] = [
  PHQ9_TEST,
  GAD7_TEST
];

const TEST_BATTERIES = [
  {
    id: 'depression-screening',
    name: 'Depression Screening Battery',
    tests: ['phq-9'],
    estimatedTime: 10,
    description: 'Comprehensive depression assessment'
  },
  {
    id: 'anxiety-screening',
    name: 'Anxiety Screening Battery',
    tests: ['gad-7'],
    estimatedTime: 8,
    description: 'Comprehensive anxiety assessment'
  },
  {
    id: 'mood-screening',
    name: 'Mood Disorders Screening',
    tests: ['phq-9', 'gad-7'],
    estimatedTime: 15,
    description: 'Combined depression and anxiety screening'
  }
];

const PsychTestingDashboard: React.FC<PsychTestingDashboardProps> = ({
  patientId,
  onTestSelect,
  onViewHistory,
  onViewReports
}) => {
  const [selectedPatient, setSelectedPatient] = useState<string>(patientId || '');
  const [patients, setPatients] = useState<Array<{ id: string; name: string }>>([]);
  const [recentTests, setRecentTests] = useState<PatientTestHistory[]>([]);


  useEffect(() => {
    // Load patients list
    fetchPatients();
    if (selectedPatient) {
      fetchRecentTests(selectedPatient);
    }
  }, [selectedPatient]);

  const fetchPatients = async () => {
    try {

      // This would be replaced with actual API call
      const response = await fetch('/api/patients');
      const data = await response.json();
      if (data.success) {
        setPatients(data.data.patients.map((p: any) => ({
          id: p.id,
          name: `${p.firstName} ${p.lastName}`
        })));
      }
    } catch (error) {
      console.error('Error fetching patients:', error);
    }
  };

  const fetchRecentTests = async (patientId: string) => {
    try {
      // This would be replaced with actual API call
      const response = await fetch(`/api/psych-tests/patient/${patientId}/recent`);
      const data = await response.json();
      if (data.success) {
        setRecentTests(data.data);
      }
    } catch (error) {
      console.error('Error fetching recent tests:', error);
    }
  };

  const handleTestStart = (testId: string) => {
    if (!selectedPatient) {
      alert('Please select a patient first');
      return;
    }
    onTestSelect(testId, selectedPatient);
  };

  const handleBatteryStart = (batteryId: string) => {
    const battery = TEST_BATTERIES.find(b => b.id === batteryId);
    if (!battery || !selectedPatient) return;
    
    // For now, start with the first test in the battery
    if (battery.tests.length > 0) {
      onTestSelect(battery.tests[0], selectedPatient);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'depression': return <TrendingUp className="h-4 w-4" />;
      case 'anxiety': return <AlertTriangle className="h-4 w-4" />;
      case 'cognitive': return <Brain className="h-4 w-4" />;
      default: return <ClipboardList className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'depression': return 'bg-blue-100 text-blue-800';
      case 'anxiety': return 'bg-yellow-100 text-yellow-800';
      case 'cognitive': return 'bg-purple-100 text-purple-800';
      case 'trauma': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Psychological Testing</h1>
          <p className="text-gray-600">Administer and manage psychological assessments</p>
        </div>
        <div className="flex items-center space-x-3">
          <Select value={selectedPatient} onValueChange={setSelectedPatient}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Select a patient..." />
            </SelectTrigger>
            <SelectContent>
              {patients.map(patient => (
                <SelectItem key={patient.id} value={patient.id}>
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span>{patient.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {selectedPatient && (
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                onClick={() => onViewHistory(selectedPatient)}
                className="flex items-center space-x-2"
              >
                <BarChart3 className="h-4 w-4" />
                <span>History</span>
              </Button>
              <Button 
                variant="outline" 
                onClick={() => onViewReports(selectedPatient)}
                className="flex items-center space-x-2"
              >
                <FileText className="h-4 w-4" />
                <span>Reports</span>
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Quick Stats */}
      {selectedPatient && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center space-x-2">
                <ClipboardList className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Tests</p>
                  <p className="text-2xl font-bold">12</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600">This Month</p>
                  <p className="text-2xl font-bold">3</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Risk Flags</p>
                  <p className="text-2xl font-bold">1</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Trend</p>
                  <p className="text-2xl font-bold">Stable</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Test Batteries */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5" />
            <span>Test Batteries</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {TEST_BATTERIES.map(battery => (
              <div key={battery.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">{battery.name}</h3>
                  <Badge variant="secondary">{battery.estimatedTime} min</Badge>
                </div>
                <p className="text-sm text-gray-600 mb-3">{battery.description}</p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">{battery.tests.length} tests</span>
                  <Button 
                    size="sm" 
                    onClick={() => handleBatteryStart(battery.id)}
                    disabled={!selectedPatient}
                  >
                    Start Battery
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Individual Tests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ClipboardList className="h-5 w-5" />
            <span>Individual Tests</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {AVAILABLE_TESTS.map(test => (
              <div key={test.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-2 mb-2">
                  {getCategoryIcon(test.category)}
                  <h3 className="font-medium">{test.name}</h3>
                </div>
                
                <div className="flex items-center space-x-2 mb-2">
                  <Badge className={getCategoryColor(test.category)}>
                    {test.category}
                  </Badge>
                  {test.timeLimit && (
                    <Badge variant="outline">
                      <Clock className="h-3 w-3 mr-1" />
                      {test.timeLimit} min
                    </Badge>
                  )}
                </div>
                
                <p className="text-sm text-gray-600 mb-3">{test.description}</p>
                
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">{test.items.length} items</span>
                  <Button 
                    size="sm" 
                    onClick={() => handleTestStart(test.id)}
                    disabled={!selectedPatient}
                  >
                    Start Test
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      {selectedPatient && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Test Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentTests.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No recent test activity</p>
              ) : (
                recentTests.map((_, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="flex-1">
                        <p className="font-medium">Recent Tests</p>
                        <p className="text-sm text-gray-600">Patient assessment history</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PsychTestingDashboard;
